import type { UseFormWatch } from 'react-hook-form';

import type { FeesFormData } from './fees-management-types';
import { formatBpsToPercent, formatTonValue } from './fees-management-utils';

interface FeesSummaryDisplayProps {
  watch: UseFormWatch<FeesFormData>;
}

export const FeesSummaryDisplay = ({ watch }: FeesSummaryDisplayProps) => {
  return (
    <div className="p-4 rounded-lg">
      <h4 className="font-medium mb-3">Current Configuration Summary</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
        <div>• Deposit Fee: {formatTonValue(watch('deposit_fee'))} TON</div>
        <div>• Withdraw Fee: {formatTonValue(watch('withdrawal_fee'))} TON</div>
        <div>• Purchase Fee: {formatBpsToPercent(watch('purchase_fee'))}%</div>
        <div>• Referral Fee: {formatBpsToPercent(watch('referrer_fee'))}%</div>
        <div>
          • Cancel Fee: {formatBpsToPercent(watch('cancel_order_fee'))}%
        </div>
        <div>
          • Fixed Cancel Fee: {formatTonValue(watch('fixed_cancel_order_fee'))}{' '}
          TON
        </div>
        <div>
          • Cancel Proposal Fee:{' '}
          {formatTonValue(watch('cancel_price_proposal_fee'))} TON
        </div>
        <div>
          • Min Deposit: {formatTonValue(watch('min_deposit_amount'))} TON
        </div>
        <div>
          • Min Withdrawal: {formatTonValue(watch('min_withdrawal_amount'))} TON
        </div>
        <div>
          • Max Withdrawal: {formatTonValue(watch('max_withdrawal_amount'))} TON
        </div>
        <div>
          • Resell Purchase Fee:{' '}
          {formatBpsToPercent(watch('resell_purchase_fee'))}%
        </div>
        <div>
          • Resell Fee for Seller:{' '}
          {formatBpsToPercent(watch('resell_purchase_fee_for_seller'))}%
        </div>
        <div>
          • Buyer Lock: {formatBpsToPercent(watch('buyer_lock_percentage'))}% of
          order amount
        </div>
        <div>
          • Seller Lock: {formatBpsToPercent(watch('seller_lock_percentage'))}%
          of order amount
        </div>
        <div>
          • Secondary Market Min Price:{' '}
          {formatTonValue(watch('min_secondary_market_price'))} TON
        </div>
        <div>• Default Lock Period: {watch('lock_period')} days</div>
      </div>
    </div>
  );
};
